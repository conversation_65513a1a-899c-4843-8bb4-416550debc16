import React, { useState } from "react";
import { Modal } from "react-bootstrap";
import { FILE_IMAGE } from "../../utils/Constant";
import <PERSON><PERSON>ie<PERSON>, { DocViewerRenderers } from "react-doc-viewer";

type Props = {
  show: boolean;
  onHide: () => void;
  file: any;
};

const ViewFile = (props: Props) => {
  let { show, onHide, file } = props;

  return (
    <Modal show={show} onHide={onHide} centered size="lg">
      <Modal.Header closeButton>
        <Modal.Title>Xem bằng chứng</Modal.Title>
      </Modal.Header>

      <Modal.Body className="spaces h-calc-vh-219">
        <div className="d-flex flex-grow-1 h-100">
          {/* {FILE_IMAGE.includes(file?.type) ? ( */}
          {/* // <img */}
          {/* //   src={file?.url}
            //   alt={file?.name}
            //   width={"100%"}
            //   style={{ objectFit: "contain" }}
            // />
          // ) : (
            // <iframe */}
          {/* //   title={file?.name}
            //   src={`${file?.url ? `${file?.url}#toolbar=0&navpanes=0` : ""}`}
            //   width="100%"
            //   height="100%"
            // /> */}
          <DocViewer
            className="w-100"
            pluginRenderers={DocViewerRenderers}
            documents={[{ uri: file?.url, fileType: file?.type }]}
          />
          {/* // )} */}
        </div>
      </Modal.Body>

      <Modal.Footer>
        <button className="btn-outline" onClick={() => onHide()}>
          Đóng
        </button>
      </Modal.Footer>
    </Modal>
  );
};

export default ViewFile;
