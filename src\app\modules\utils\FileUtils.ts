import { toast } from "react-toastify";
import { actualFileType, DEFAULT_FILE_TYPES, ERROR_MESSAGE } from "./Constant";
import { fileUpload, getFileById } from "../services/fileServices";
import { IFile, IViewFile } from "../models/models";

export const handleUploadFileUtil = async (
  file: File,
  setIsLoading: (loading: boolean) => void
): Promise<IFile | null> => {
  const isValidMaxSize = file.size > 20 * 1024 * 1024; // 20MB

  const isValidFileType = actualFileType.find(
    (item) => DEFAULT_FILE_TYPES.includes(item.id) && item.value === file.type
  );

  if (!isValidFileType) {
    toast.error("Sai kiểu tệp tin, vui lòng thử lại!");
    return null;
  }

  if (isValidMaxSize) {
    toast.error("Tệp tin quá lớn, vui lòng thử lại!");
    return null;
  }

  setIsLoading(true);
  try {
    const {
      data: { data },
    } = await fileUpload(file);

    return {
      id: data?.id,
      name: data?.name,
    };
  } catch (error) {
    console.error("Upload file error", error);
    toast.error(ERROR_MESSAGE);
    return null;
  } finally {
    setIsLoading(false);
  }
};

export const handleViewFileUtil = async (
  file: IFile,
  setIsLoading: (loading: boolean) => void
): Promise<IViewFile | null> => {
  setIsLoading(true);
  try {
    const { data } = await getFileById(file?.id);
    const ext = file?.name.split(".").pop()?.toLowerCase();

    const mimeTypes: Record<string, string> = {
      pdf: "application/pdf",
      docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      doc: "application/msword",
      xls: "application/vnd.ms-excel",
    };

    const mimeType = mimeTypes[ext || ""] || "application/octet-stream";

    const blob = new Blob([data], { type: mimeType });
    const fileURL = URL.createObjectURL(blob);

    return {
      open: true,
      file: {
        id: file?.id,
        name: file?.name,
        type: file?.name.split(".")?.pop(),
        url: fileURL,
      },
    };
  } catch (error) {
    console.error("View file error", error);
    toast.error(ERROR_MESSAGE);
    return null;
  } finally {
    setIsLoading(false);
  }
};
